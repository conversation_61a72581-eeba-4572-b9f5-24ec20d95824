/**
 * 住宿预订相关的类型定义
 */

// API response from backend
export interface HotelApiResponse {
  code: number
  msg: string
  data: HotelRoomData[]
}

// Hotel room data from API
export interface HotelRoomData {
  room_id: number
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  checkin_date: string
  checkout_date: string
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  Id: number
  room_type: string
}

// Processed hotel information for UI
export interface Hotel {
  id: number
  name: string
  chineseName: string
  address: string
  tel: string
  imageUrl: string
  coordinates: {
    lat: number
    lng: number
  }
  defaultCheckinDate?: string
  defaultCheckoutDate?: string
  roomTypes: RoomType[]
}

// Room type information
export interface RoomType {
  id: string // Room type ID
  type: string // Room type name
  price: number
  total: number
  obligate: number
  checkinDate: string
  checkoutDate: string
}

// Shared room options (API uses numbers)
export type SharedOption = 1 | 2 | 3 // 1: none, 2: with_partner, 3: system_assigned

// Booking request type for API
export interface BookingRequest {
  room_id: number
  shared_option: SharedOption
  occupant: number // User ID
  checkin_date: string
  checkout_date: string
  is_assigned: boolean
  partner_user_id?: number // Only when shared_option is 2 (with_partner)
}

// UI form data type
export interface BookingFormData {
  checkinDate: string
  checkoutDate: string
  sharedOptions: "none" | "with_partner" | "system_assigned"
  partnerUsername: string
  isTeamBooking: boolean
  roomCount: number
}

// API response for user booking
export interface UserBookingApiResponse {
  code: number
  msg: string
  data: UserBookingData | null
}

// User booking data from API
export interface UserBookingData {
  id: number
  executor_username: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  RoomId: number
  SharedOption: number
  AssignedRoomId: number
  occupant: number
  is_assigned: boolean
  room_type: string
  shared_option: string
  username: string
  gender: string
  country: string
  organization: string
  roommate?: RoommateData
}

// Roommate data
export interface RoommateData {
  id: number
  executor_username: string
  Id: number
  RoomId: number
  SharedOption: number
  AssignedRoomId: number
  occupant: number
  checkin_date: string
  checkout_date: string
  is_assigned: boolean
  username: string
  name: string
  gender: string
  country: string
  organization: string
}

// Processed booking record for UI display
export interface BookingRecord {
  id: number
  hotelName: string
  chineseName: string
  location: string
  contactPhone: string
  imageUrl: string
  roomType: string
  sharedOption: string
  price: number
  checkinDate: string
  checkoutDate: string
  assignedRoomId: number
  isAssigned: boolean
  user: {
    username: string
    gender: string
    country: string
    organization: string
  }
  roommate?: {
    username: string
    name: string
    gender: string
    country: string
    organization: string
  }
}

// API 响应类型
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
  message?: string
}

// 用户名验证响应类型
export interface UsernameValidationResponse {
  username: string
  exists: boolean
  name?: string
}
