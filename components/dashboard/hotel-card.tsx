"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Hotel, RoomType } from "@/types/accommodation"

interface HotelCardProps {
  hotel: Hotel
  onBookRoom: (hotel: Hotel, roomType: RoomType) => void
}

export default function HotelCard({ hotel, onBookRoom }: HotelCardProps) {
  const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(null)

  // Check if hotel has any available rooms
  const hasAvailableRooms = hotel.roomTypes.some((roomType) => roomType.total > 0)

  const handleBookNow = () => {
    if (selectedRoomType) {
      onBookRoom(hotel, selectedRoomType)
    }
  }

  return (
    <Card className="flex h-full flex-col overflow-hidden">
      {/* Hotel image */}
      <div className="relative h-48 bg-gray-200">
        {hotel.id === 1 && (
          <div className="absolute top-3 left-3 z-10">
            <Badge className="bg-green-600">Recommended</Badge>
          </div>
        )}
        <div
          className="h-full w-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://minioapi.bugmaker.me${hotel.imageUrl}')`,
          }}
        />
      </div>

      <CardHeader className="pb-4">
        {/* Fixed height title area to ensure alignment */}
        <div className="flex h-14 items-start">
          <CardTitle className="line-clamp-2 text-lg leading-tight">{hotel.name}</CardTitle>
        </div>
        <div className="mb-2 text-sm text-gray-600">{hotel.chineseName}</div>
        <CardDescription className="text-sm">
          <div className="space-y-1">
            <div className="flex items-start">
              <i className="fas fa-map-marker-alt mt-1 mr-2 flex-shrink-0 text-gray-400"></i>
              <span className="line-clamp-2">{hotel.address}</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-phone mr-2 flex-shrink-0 text-gray-400"></i>
              <span>{hotel.tel}</span>
            </div>
          </div>
        </CardDescription>
      </CardHeader>

      <CardContent className="flex flex-1 flex-col">
        {/* Room type selection */}
        <div className="flex-1 space-y-3">
          <h4 className="font-medium text-gray-900">Available Room Types</h4>
          {hotel.roomTypes.map((roomType, index) => (
            <div
              key={index}
              className={`cursor-pointer rounded-lg border p-3 transition-colors ${
                roomType.total === 0
                  ? "cursor-not-allowed border-gray-200 bg-gray-50 opacity-60"
                  : selectedRoomType?.id === roomType.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => roomType.total > 0 && setSelectedRoomType(roomType)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name={`room-${hotel.id}`}
                      checked={selectedRoomType?.id === roomType.id}
                      onChange={() => roomType.total > 0 && setSelectedRoomType(roomType)}
                      disabled={roomType.total === 0}
                      className="mr-3"
                    />
                    <div>
                      <p className={`font-medium ${roomType.total === 0 ? "text-gray-500" : "text-gray-900"}`}>
                        {roomType.type}
                      </p>
                      {roomType.total === 0 && <p className="text-xs text-red-500">No rooms available</p>}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-lg font-bold ${roomType.total === 0 ? "text-gray-500" : "text-gray-900"}`}>
                    ¥{roomType.price}
                    <span className="text-sm font-normal text-gray-500">/night</span>
                  </p>
                  <p className={`text-xs ${roomType.total === 0 ? "text-gray-500" : "text-green-600"}`}>
                    {roomType.total === 0 ? "Sold out" : "Conference rate"}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Booking button - positioned at bottom */}
        <div className="mt-4 border-t pt-4">
          <Button
            className="w-full"
            onClick={handleBookNow}
            disabled={!hasAvailableRooms || !selectedRoomType || (selectedRoomType && selectedRoomType.total === 0)}
          >
            <i className="fas fa-hotel mr-2"></i>
            {!hasAvailableRooms
              ? "Rooms Full"
              : !selectedRoomType
                ? "Select Room Type"
                : selectedRoomType.total === 0
                  ? "No Rooms Available"
                  : "Book Selected Room"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
