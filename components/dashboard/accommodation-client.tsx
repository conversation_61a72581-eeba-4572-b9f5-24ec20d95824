"use client"

import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import {
  BookingRecord,
  Hotel,
  HotelApiResponse,
  HotelRoomData,
  RoomType,
  UserBookingApiResponse,
  UserBookingData,
} from "@/types/accommodation"
import { getAuthToken } from "@/utils/auth"
import { api } from "@/utils/api"
import { getHomePageImageUrl, getHotelImageUrl } from "@/utils/minio"
import BookingForm from "./booking-form"
import HotelCard from "./hotel-card"

type ViewMode = "hotels" | "booking" | "bookings"

interface BookingSelection {
  hotel: Hotel
  roomType: RoomType
}

export default function AccommodationClient() {
  const { user } = useUser()
  const { addToast } = useToast()
  const [viewMode, setViewMode] = useState<ViewMode>("hotels")
  const [hotels, setHotels] = useState<Hotel[]>([])
  const [bookings, setBookings] = useState<BookingRecord[]>([])
  const [isLoadingHotels, setIsLoadingHotels] = useState(true)
  const [isLoadingBookings, setIsLoadingBookings] = useState(false)
  const [bookingSelection, setBookingSelection] = useState<BookingSelection | null>(null)

  // Load hotel information from API
  const loadHotels = async () => {
    setIsLoadingHotels(true)
    try {
      const result = await api.get<HotelRoomData[]>("/api/accommodations")

      if (result) {
        // Process API data to group by hotel
        const processedHotels = processHotelData(result)
        setHotels(processedHotels)
      }
    } catch (error) {
      console.error("Load hotels error:", error)
      addToast({
        type: "error",
        title: "Load Error",
        message: "An error occurred while loading hotel information. Please try again.",
      })
    } finally {
      setIsLoadingHotels(false)
    }
  }

  // Process API data to group room types by hotel
  const processHotelData = (data: HotelRoomData[]): Hotel[] => {
    const hotelMap = new Map<number, Hotel>()

    data.forEach((item) => {
      const hotelId = item.HotelId

      if (!hotelMap.has(hotelId)) {
        hotelMap.set(hotelId, {
          id: hotelId,
          name: item.name,
          chineseName: item.chinese_name,
          address: item.location,
          tel: item.contact_phone,
          imageUrl: item.image_url,
          coordinates: {
            lat: item.lat,
            lng: item.lon,
          },
          defaultCheckinDate: item.default_checkin_date,
          defaultCheckoutDate: item.default_checkout_date,
          roomTypes: [],
        })
      }

      const hotel = hotelMap.get(hotelId)!
      // Ensure total is not negative
      const totalRooms = Math.max(0, item.total)
      hotel.roomTypes.push({
        id: `${item.room_id}`, // Use room_id directly
        type: `${item.room_type} (${totalRooms} rooms)`,
        price: item.price,
        total: totalRooms,
        obligate: item.obligate,
        checkinDate: item.checkin_date,
        checkoutDate: item.checkout_date,
      })
    })

    return Array.from(hotelMap.values())
  }

  // Load user booking records from API
  const loadBookings = async () => {
    if (!user?.id) {
      console.log("No user ID available for loading bookings")
      return
    }

    console.log("Loading bookings for user:", user.id)
    setIsLoadingBookings(true)
    try {
      const result = await api.get<UserBookingData | null>("/api/accommodations/me", { user_id: user.id.toString() })
      console.log("API response for bookings:", result)

      if (result && result.room_id && result.chinese_name) {
        // Process API data to UI format
        const processedBooking = processBookingData(result)
        console.log("Processed booking data:", processedBooking)
        setBookings([processedBooking])
      } else {
        // No booking found or empty data
        console.log("No valid booking data found:", result)
        setBookings([])
      }
    } catch (error) {
      console.error("Load bookings error:", error)
      // Set empty bookings on error to show "No bookings yet" message
      setBookings([])
      const errorMessage = error instanceof Error ? error.message : "Unknown error"
      addToast({
        type: "error",
        title: "Load Error",
        message: `An error occurred while loading your bookings: ${errorMessage}`,
      })
    } finally {
      setIsLoadingBookings(false)
    }
  }

  // Process API booking data to UI format
  const processBookingData = (data: UserBookingData): BookingRecord => {
    console.log("Processing booking data:", data)
    console.log("Image URL from API:", data.image_url)

    const processed = {
      id: data.room_id || data.RoomId || 0, // Use room_id as the primary identifier
      hotelName: data.chinese_name,
      chineseName: data.chinese_name,
      location: data.location,
      contactPhone: data.contact_phone,
      imageUrl: data.image_url,
      roomType: data.room_type,
      sharedOption: data.shared_option,
      price: data.price,
      checkinDate: data.default_checkin_date,
      checkoutDate: data.default_checkout_date,
      assignedRoomId: data.AssignedRoomId,
      isAssigned: data.is_assigned,
      user: {
        username: data.username,
        gender: data.gender,
        country: data.country,
        organization: data.organization,
      },
      roommate: data.roommate
        ? {
            username: data.roommate.username,
            name: data.roommate.name,
            gender: data.roommate.gender,
            country: data.roommate.country,
            organization: data.roommate.organization || "Not specified",
          }
        : undefined,
    }

    console.log("Processed imageUrl:", processed.imageUrl)
    console.log("Generated hotel image URL:", getHotelImageUrl(processed.imageUrl))

    return processed
  }

  // Handle room booking
  const handleBookRoom = (hotel: Hotel, roomType: RoomType) => {
    setBookingSelection({ hotel, roomType })
    setViewMode("booking")
  }

  // Handle booking success
  const handleBookingSuccess = () => {
    setViewMode("bookings")
    setBookingSelection(null)
    loadBookings() // 重新加载预订记录
  }

  // Handle cancel booking
  const handleCancelBooking = () => {
    setViewMode("hotels")
    setBookingSelection(null)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Initialize loading
  useEffect(() => {
    loadHotels()
  }, [])

  // Load data when switching to bookings view
  useEffect(() => {
    if (viewMode === "bookings") {
      loadBookings()
    }
  }, [viewMode, user?.id])

  if (!user) {
    return (
      <div className="py-12 text-center">
        <p className="text-gray-500">Please login to access accommodation booking.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Navigation tabs */}
      <div className="flex space-x-1 rounded-lg bg-gray-100 p-1">
        <button
          onClick={() => setViewMode("hotels")}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            viewMode === "hotels" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <i className="fas fa-hotel mr-2"></i>
          Browse Hotels
        </button>
        <button
          onClick={() => setViewMode("bookings")}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            viewMode === "bookings" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <i className="fas fa-bed mr-2"></i>
          My Bookings
        </button>
      </div>

      {/* Hotels browsing view */}
      {viewMode === "hotels" && (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900">Conference Hotels</h2>
            <p className="text-gray-600">Special rates for IFMB 2025 attendees</p>
          </div>

          {isLoadingHotels ? (
            <div className="py-12 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
              <p className="mt-2 text-gray-500">Loading hotels...</p>
            </div>
          ) : hotels.length > 0 ? (
            <div className="grid auto-rows-fr grid-cols-1 gap-6 lg:grid-cols-2">
              {hotels.map((hotel) => (
                <HotelCard key={hotel.id} hotel={hotel} onBookRoom={handleBookRoom} />
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <i className="fas fa-hotel mb-4 text-5xl text-gray-400"></i>
              <p className="text-gray-500">No hotels available at the moment.</p>
            </div>
          )}
        </div>
      )}

      {/* Booking form view */}
      {viewMode === "booking" && bookingSelection && (
        <div>
          <div className="mb-6">
            <Button variant="outline" onClick={() => setViewMode("hotels")} className="mb-4">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Hotels
            </Button>
          </div>

          <BookingForm
            hotel={bookingSelection.hotel}
            roomType={bookingSelection.roomType}
            onBookingSuccess={handleBookingSuccess}
            onCancel={handleCancelBooking}
          />
        </div>
      )}

      {/* My bookings view */}
      {viewMode === "bookings" && (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900">My Bookings</h2>
            <p className="text-gray-600">Manage your hotel reservations</p>
          </div>

          {isLoadingBookings ? (
            <div className="py-12 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
              <p className="mt-2 text-gray-500">Loading bookings...</p>
            </div>
          ) : bookings.length > 0 ? (
            <div className="space-y-6">
              {bookings.map((booking) => {
                console.log("Rendering booking:", booking)
                console.log("Booking imageUrl:", booking.imageUrl)
                return (
                  <Card key={booking.id} className="overflow-hidden">
                    {/* Hotel image header */}
                    <div className="relative h-48 overflow-hidden bg-gray-200">
                      <img
                        src="https://minioapi.bugmaker.me/ifmb-2025-public/hotels/IAEC.jpg"
                        alt={booking.hotelName}
                        className="h-full w-full object-cover object-center"
                        onLoad={() => {
                          console.log("Image loaded successfully!")
                        }}
                        onError={(e) => {
                          console.log("Hotel image failed to load:", "https://minioapi.bugmaker.me/ifmb-2025-public/hotels/IAEC.jpg")
                          console.log("Original imageUrl:", booking.imageUrl)
                          console.log("Error event:", e)
                          // Fallback to a default image
                          const target = e.target as HTMLImageElement
                          target.src = getHomePageImageUrl()
                        }}
                      />
                      <div className="bg-opacity-40 absolute inset-0 bg-black" />
                      <div className="absolute bottom-4 left-4 text-white">
                        <h3 className="text-xl font-bold">{booking.hotelName}</h3>
                        <p className="text-sm opacity-90">{booking.roomType}</p>
                      </div>
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-green-600">
                          {booking.isAssigned ? "Room Assigned" : "Pending Assignment"}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      {/* Booking basic information */}
                      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                        {/* Hotel Information */}
                        <div className="space-y-4">
                          <div>
                            <h4 className="mb-3 font-semibold text-gray-900">Hotel Information</h4>
                            <div className="space-y-2">
                              <div className="flex items-start">
                                <i className="fas fa-map-marker-alt mt-1 mr-2 flex-shrink-0 text-gray-400"></i>
                                <span className="line-clamp-2 text-sm text-gray-600">{booking.location}</span>
                              </div>
                              <div className="flex items-center">
                                <i className="fas fa-phone mr-2 flex-shrink-0 text-gray-400"></i>
                                <span className="text-sm text-gray-600">{booking.contactPhone}</span>
                              </div>
                              <div className="flex items-center">
                                <i className="fas fa-door-open mr-2 flex-shrink-0 text-gray-400"></i>
                                <span className="text-sm text-gray-600">Room #{booking.assignedRoomId}</span>
                              </div>
                            </div>
                          </div>

                          {/* Booking Details */}
                          <div>
                            <h4 className="mb-3 font-semibold text-gray-900">Booking Details</h4>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Check-in:</span>
                                <span className="text-sm font-medium">{formatDate(booking.checkinDate)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Check-out:</span>
                                <span className="text-sm font-medium">{formatDate(booking.checkoutDate)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Sharing:</span>
                                <span className="text-sm font-medium capitalize">{booking.sharedOption}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Price per night:</span>
                                <span className="text-sm font-bold text-green-600">¥{booking.price}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* User Information */}
                        <div className="space-y-4">
                          <div>
                            <h4 className="mb-3 font-semibold text-gray-900">Your Information</h4>
                            <div className="rounded-lg bg-blue-50 p-4">
                              <div className="mb-2 flex items-center">
                                <i className="fas fa-user mr-2 text-blue-600"></i>
                                <span className="font-medium text-blue-900">{booking.user.username}</span>
                              </div>
                              <div className="space-y-1 text-sm text-blue-800">
                                <div className="flex justify-between">
                                  <span>Gender:</span>
                                  <span className="capitalize">{booking.user.gender}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Country:</span>
                                  <span>{booking.user.country}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Organization:</span>
                                  <span>{booking.user.organization}</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Roommate Information */}
                          {booking.roommate && (
                            <div>
                              <h4 className="mb-3 font-semibold text-gray-900">Roommate Information</h4>
                              <div className="rounded-lg bg-green-50 p-4">
                                <div className="mb-2 flex items-center">
                                  <i className="fas fa-user-friends mr-2 text-green-600"></i>
                                  <span className="font-medium text-green-900">{booking.roommate.name}</span>
                                  <span className="ml-2 text-sm text-green-700">(@{booking.roommate.username})</span>
                                </div>
                                <div className="space-y-1 text-sm text-green-800">
                                  <div className="flex justify-between">
                                    <span>Gender:</span>
                                    <span className="capitalize">{booking.roommate.gender}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Country:</span>
                                    <span>{booking.roommate.country}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Organization:</span>
                                    <span>{booking.roommate.organization || "Not specified"}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Action buttons */}
                      <div className="flex justify-end gap-3 border-t pt-4">
                        <Button variant="outline" size="sm">
                          <i className="fas fa-phone mr-2"></i>
                          Contact Hotel
                        </Button>
                        <Button variant="outline" size="sm">
                          <i className="fas fa-download mr-2"></i>
                          Download Receipt
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          ) : (
            <Card>
              <CardContent className="py-12 text-center">
                <i className="fas fa-bed mb-4 text-5xl text-gray-400"></i>
                <p className="mb-2 text-lg font-medium text-gray-500">No bookings yet</p>
                <p className="mb-6 text-gray-500">You haven't made any hotel reservations</p>
                <Button onClick={() => setViewMode("hotels")}>
                  <i className="fas fa-search mr-2"></i>
                  Browse Hotels
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
